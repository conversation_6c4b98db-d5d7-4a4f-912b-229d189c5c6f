# Departments and Units Migration

This document describes the migration from hardcoded departments to a Firestore-based departments and units system.

## Overview

The application has been updated to:
- Store departments and their associated units in Firestore instead of hardcoded arrays
- Add unit selection to project forms
- Maintain backward compatibility with existing projects

## Changes Made

### 1. New Firestore Collection: `departments`

Each department document contains:
```json
{
  "name": "Department Name",
  "units": [
    {
      "name": "Unit Name",
      "email": "<EMAIL>",
      "access": ["DEPT_CODE"],
      "invisible": false
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### 2. New Store: `src/stores/departments.js`

Manages departments and units with methods:
- `fetchDepartments()` - Load from Firestore
- `departmentOptions` - Computed list for dropdowns
- `getUnitsForDepartment(deptName)` - Get units for a department
- `getUnitOptionsForDepartment(deptName)` - Get unit options for dropdowns

### 3. Updated Project Form

- Added unit selection dropdown
- Unit dropdown populates based on selected department
- Both department and unit are now required fields
- Automatic unit clearing when department changes

### 4. Updated Configuration Store

- Removed department management (now handled by departments store)
- Departments no longer stored in `config/dropdowns` document

### 5. Updated Admin Dashboard

- Removed department management UI
- Added note about Firestore-based department management
- Updated CSV export to include unit field

## Migration Script

### Running the Population Script

```bash
npm run populate:departments
```

This script:
1. Reads `units.json` file
2. Groups units by department
3. Creates department documents in Firestore
4. Preserves all unit metadata (email, access, invisible flags)

### Data Structure Mapping

From `units.json`:
```json
{
  "UNIT_NAME": {
    "dept": "DEPARTMENT_NAME",
    "unit": "UNIT_NAME",
    "email": "<EMAIL>",
    "access": ["DEPT_CODE"],
    "invisible": false
  }
}
```

To Firestore `departments/{DEPARTMENT_NAME}`:
```json
{
  "name": "DEPARTMENT_NAME",
  "units": [
    {
      "name": "UNIT_NAME",
      "email": "<EMAIL>",
      "access": ["DEPT_CODE"],
      "invisible": false
    }
  ]
}
```

## Backward Compatibility

- Existing projects without unit field will still display correctly
- Unit field is optional in display but required for new projects
- CSV export includes unit field (empty for legacy projects)

## Files Modified

### New Files
- `src/stores/departments.js` - Departments store
- `scripts/populate-departments.js` - Migration script
- `DEPARTMENTS_MIGRATION.md` - This documentation

### Modified Files
- `src/components/ProjectForm.vue` - Added unit selection
- `src/stores/config.js` - Removed department management
- `src/views/AdminDashboard.vue` - Updated department management UI
- `src/views/ProjectList.vue` - Display unit in project list
- `src/components/ProjectDetailModal.vue` - Display unit in details
- `src/App.vue` - Load departments store on app start
- `package.json` - Added populate script

## Usage

### For Developers

1. Run the migration script once: `npm run populate:departments`
2. The app will automatically load departments from Firestore
3. Project forms now require both department and unit selection

### For Users

1. Department dropdown loads from Firestore
2. Select department first, then unit
3. Unit dropdown populates based on department selection
4. Both fields are required for new projects

## Future Enhancements

- Admin interface for managing departments and units
- Bulk import/export functionality
- Unit-based access control
- Department hierarchy support
