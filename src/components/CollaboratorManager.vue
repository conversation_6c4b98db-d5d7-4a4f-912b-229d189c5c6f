<template>
  <div class="collaborator-section">
    <div class="flex justify-content-between align-items-center mb-4">
      <h3 class="text-lg font-semibold text-color">Collaborators</h3>
      <Button @click="addCollaborator" icon="pi pi-plus" label="Add Collaborator" size="small" />
    </div>

    <div v-if="model.length === 0" class="text-color-secondary text-center p-4">
      No collaborators added yet
    </div>

    <div v-else class="collaborator-list">
      <Card
        v-for="(collaborator, index) in model"
        :key="'collaborator_' + index"
        class="collaborator-card mb-4"
      >
        <template #content>
          <div class="grid md-grid-cols-3 gap-4">
            <!-- Rank -->
            <div class="form-field">
              <label class="form-label"> Rank * </label>
              <PrimeSelect
                v-model="collaborator.rank"
                :options="ranksStore.rankOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="Select rank"
                @change="validateField(index, 'rank')"
              />
              <small v-if="getFieldError(index, 'rank')" class="form-error">
                {{ getFieldError(index, 'rank') }}
              </small>
            </div>

            <!-- Full Name -->
            <div class="form-field">
              <label class="form-label"> Full Name * </label>
              <InputText
                v-model="collaborator.fullName"
                placeholder="Enter full name"
                @blur="validateField(index, 'fullName')"
                />
              <small v-if="getFieldError(index, 'fullName')" class="form-error">
                {{ getFieldError(index, 'fullName') }}
              </small>
            </div>

            <!-- Employee Number -->
            <div class="form-field">
              <label class="form-label"> Employee Number * </label>
              <div class="flex gap-4">
                <InputText
                  v-model="collaborator.employeeNumber"
                  placeholder="6-digit number"
                  maxlength="6"
                  @blur="validateField(index, 'employeeNumber')"
                />
                <Button
                  @click="removeCollaborator(index)"
                  icon="pi pi-trash"
                  severity="danger"
                  size="small"
                  v-tooltip="'Remove collaborator'"
                />
              </div>
              <small v-if="getFieldError(index, 'employeeNumber')" class="form-error">
                {{ getFieldError(index, 'employeeNumber') }}
              </small>
            </div>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>



<script setup>

import { ref, onMounted } from 'vue'
import { useRanksStore } from '@/stores/ranks'

const model = defineModel()




const ranksStore = useRanksStore()
const errors = ref({})

// Load ranks on component mount
onMounted(async () => {
  if (ranksStore.ranks.length === 0) {
    await ranksStore.fetchRanks()
  }
})


function addCollaborator() {
  model.value.push({
    rank: '',
    fullName: '',
    employeeNumber: '',
  })
}



function removeCollaborator(index) {
  model.value.splice(index, 1)
  // Clean up errors for removed collaborator
  Object.keys(errors.value).forEach((key) => {
    if (key.startsWith(`${index}.`)) {
      delete errors.value[key]
    }
  })
}

function validateField(index, field) {
  const collaborator = model.value[index]
  const key = `${index}.${field}`

  // Clear previous error
  delete errors.value[key]

  switch (field) {
    case 'rank':
      if (!collaborator.rank) {
        errors.value[key] = 'Rank is required'
      }
      break
    case 'fullName':
      if (!collaborator.fullName || collaborator.fullName.trim().length < 2) {
        errors.value[key] = 'Full name is required (minimum 2 characters)'
      }
      break
    case 'employeeNumber':
      if (!collaborator.employeeNumber) {
        errors.value[key] = 'Employee number is required'
      } else if (!/^\d{6}$/.test(collaborator.employeeNumber)) {
        errors.value[key] = 'Employee number must be exactly 6 digits'
      } else {
        // Check for duplicates
        const duplicateIndex = model.value.findIndex(
          (c, i) => i !== index && c.employeeNumber === collaborator.employeeNumber,
        )
        if (duplicateIndex !== -1) {
          errors.value[key] = 'Employee number already exists'
        }
      }
      break
  }
}

function getFieldError(index, field) {
  const errorKey = `${index}.${field}`
  const error = errors.value[errorKey]
  return error
}

function validateAll() {
  errors.value = {}
  let isValid = true

  model.value.forEach((_, index) => {
    ;['rank', 'fullName', 'employeeNumber'].forEach((field) => {
      validateField(index, field)
      if (getFieldError(index, field)) {
        isValid = false
      }
    })
  })

  return isValid
}

// Expose validation method to parent
defineExpose({
  validateAll,
  hasErrors: () => Object.keys(errors.value).length > 0,
})
</script>

<style scoped>
.collaborator-section {
  margin-bottom: 1.5rem;
}

.collaborator-list {
  margin-top: 1rem;
}

.collaborator-card {
  border: 1px solid var(--p-surface-border);
  border-radius: var(--p-border-radius);
  transition: all 0.2s ease;
}

.collaborator-card:hover {
  border-color: var(--p-primary-200);
  box-shadow: var(--p-shadow-2);
}

/* Add spacing between collaborator title and button */
.collaborator-section h3 {
  margin: 0;
}

/* Ensure proper spacing in the header */
.collaborator-section .flex {
  padding: 0rem 0;
}
</style>
