<script setup>
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useConfigStore } from '@/stores/config'
import { useDepartmentsStore } from '@/stores/departments'
import AppNavigation from '@/components/AppNavigation.vue'
// import FirebaseStatus from '@/components/FirebaseStatus.vue'
// import DevTools from '@/components/DevTools.vue'

const authStore = useAuthStore()
const configStore = useConfigStore()
const departmentsStore = useDepartmentsStore()

onMounted(async () => {
  // Initialize authentication state
  await authStore.initializeAuth()

  // Load configuration
  await configStore.loadConfig()

  // Load departments and units
  await departmentsStore.fetchDepartments()
})
</script>

<template>
  <div class="app-container">
    <AppNavigation />

    <main class="container p-6">
      <RouterView />
    </main>

    <!-- Global Toast for notifications -->
    <Toast />

    <!-- Global Confirm Dialog -->
    <ConfirmDialog />

    <!-- Development Tools -->
    <!-- <DevTools /> -->

    <!-- Firebase Status (Development Only) -->
    <!-- <FirebaseStatus /> -->
  </div>
</template>

<style>
/* App Layout */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--p-surface-50), var(--p-primary-50));
}

/* Global PrimeVue Component Styles */
.p-button {
  font-weight: 500;
}

.p-inputtext {
  width: 100%;
}

.p-select {
  width: 100%;
}

.p-datepicker {
  width: 100%;
}

.p-textarea {
  width: 100%;
}

/* Responsive table styles */
.p-datatable .p-datatable-tbody > tr > td {
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .p-datatable .p-datatable-tbody > tr > td {
    font-size: 0.75rem;
  }
}

/* Form styles using PrimeVue variables */
.form-field {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin-bottom: 0.5rem;
}

.form-error {
  color: var(--p-red-500);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Loading spinner using PrimeVue variables */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border: 2px solid var(--p-surface-200);
  border-bottom: 2px solid var(--p-primary-color);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* PrimeVue Component Enhancements */
.p-button.p-button-text {
  color: var(--p-text-color);
}

.p-button.p-button-text:hover {
  color: var(--p-primary-color);
  background-color: var(--p-primary-50);
}

.p-button.p-button-outlined {
  color: var(--p-primary-color);
  border-color: var(--p-primary-color);
}

.p-button.p-button-outlined:hover {
  background-color: var(--p-primary-50);
  border-color: var(--p-primary-color);
}

/* DataTable styling */
.p-datatable .p-datatable-header {
  color: var(--p-text-color);
}

.p-datatable .p-datatable-tbody > tr > td {
  color: var(--p-text-color);
}

/* Select styling */
.p-select-label {
  color: var(--p-text-color);
}

/* Removed deprecated dropdown styles - now using PrimeSelect */

/* Status badges */
.p-tag {
  font-weight: 600;
}

/* Form labels */
label.form-label {
  color: var(--p-text-color);
  font-weight: 600;
}

/* Enhanced Select Styling */
.p-select-overlay {
  border: 1px solid var(--p-surface-border);
  box-shadow: var(--p-shadow-4);
}

.p-select-overlay .p-select-option {
  color: var(--p-text-color);
  padding: 0.75rem 1rem;
  font-weight: 500;
}

.p-select-overlay .p-select-option:hover {
  background-color: var(--p-primary-50);
  color: var(--p-primary-color);
}

.p-select-overlay .p-select-option.p-selected {
  background-color: var(--p-primary-100);
  color: var(--p-primary-color);
}

/* Select trigger styling */
.p-select .p-select-dropdown {
  color: var(--p-text-muted-color);
}

.p-select:hover .p-select-dropdown {
  color: var(--p-text-color);
}

/* Select focus states */
.p-select {
  border-color: var(--p-surface-border);
}

.p-select:not(.p-disabled):hover {
  border-color: var(--p-surface-400);
}

.p-select:not(.p-disabled).p-focus {
  border-color: var(--p-primary-color);
  box-shadow: 0 0 0 2px var(--p-primary-100);
}
</style>
