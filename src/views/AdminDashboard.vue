<template>
  <div>
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl md:text-3xl font-bold text-secondary-800 mb-2">Admin Dashboard</h1>
      <p class="text-secondary-600">Manage projects, configurations, and system settings</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <Card
        class="bg-gradient-to-br from-primary-50 to-primary-100 border-primary-200 shadow-sm hover:shadow-md transition-shadow"
      >
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center">
                <i class="pi pi-file text-white text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <div class="text-sm font-semibold text-primary-700">Total Projects</div>
              <div class="text-3xl font-bold text-primary-900">
                {{ projectsStore.projectCount }}
              </div>
            </div>
          </div>
        </template>
      </Card>

      <Card
        class="bg-gradient-to-br from-success-50 to-success-100 border-success-200 shadow-sm hover:shadow-md transition-shadow"
      >
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-success-500 rounded-xl flex items-center justify-center">
                <i class="pi pi-check-circle text-white text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <div class="text-sm font-semibold text-success-700">Completed</div>
              <div class="text-3xl font-bold text-success-900">
                {{ projectsStore.completedProjects.length }}
              </div>
            </div>
          </div>
        </template>
      </Card>

      <Card
        class="bg-gradient-to-br from-warning-50 to-warning-100 border-warning-200 shadow-sm hover:shadow-md transition-shadow"
      >
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-warning-500 rounded-xl flex items-center justify-center">
                <i class="pi pi-clock text-white text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <div class="text-sm font-semibold text-warning-700">In Progress</div>
              <div class="text-3xl font-bold text-warning-900">
                {{ projectsStore.inProgressProjects.length }}
              </div>
            </div>
          </div>
        </template>
      </Card>

      <Card
        class="bg-gradient-to-br from-secondary-50 to-secondary-100 border-secondary-200 shadow-sm hover:shadow-md transition-shadow"
      >
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-secondary-500 rounded-xl flex items-center justify-center">
                <i class="pi pi-building text-white text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <div class="text-sm font-semibold text-secondary-700">Departments</div>
              <div class="text-3xl font-bold text-secondary-900">
                Managed in Firestore
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Admin Panels -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Project Management -->
      <Card>
        <template #title>
          <div class="flex items-center space-x-2">
            <i class="pi pi-folder text-blue-600"></i>
            <span>Project Management</span>
          </div>
        </template>
        <template #content>
          <div class="space-y-4">
            <Button
              @click="exportProjects"
              icon="pi pi-download"
              label="Export All Projects (CSV)"
              class="w-full p-button-outlined"
              :loading="exportLoading"
            />

            <Button
              @click="showBulkDeleteDialog = true"
              icon="pi pi-trash"
              label="Bulk Delete Projects"
              class="w-full p-button-danger p-button-outlined"
              severity="danger"
            />

            <div class="pt-4 border-t">
              <h4 class="font-medium text-gray-900 mb-2">Quick Actions</h4>
              <div class="space-y-2">
                <Button
                  @click="$router.push('/projects')"
                  icon="pi pi-list"
                  label="View All Projects"
                  class="w-full p-button-text"
                />
                <Button
                  @click="$router.push('/register')"
                  icon="pi pi-plus"
                  label="Register New Project"
                  class="w-full p-button-text"
                />
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Configuration Management -->
      <Card>
        <template #title>
          <div class="flex items-center space-x-2">
            <i class="pi pi-cog text-green-600"></i>
            <span>Configuration</span>
          </div>
        </template>
        <template #content>
          <div class="space-y-4">
            <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">📋 Departments & Units</h4>
              <p class="text-sm text-blue-700 mb-2">
                Departments and units are now managed through the Firestore 'departments' collection.
              </p>
              <p class="text-xs text-blue-600">
                Use the populate script to import from units.json or manage directly in Firestore.
              </p>
            </div>

            <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 class="font-medium text-green-900 mb-2">👥 Ranks</h4>
              <p class="text-sm text-green-700 mb-2">
                Ranks are now managed through the Firestore 'ranks' collection.
              </p>
              <p class="text-xs text-green-600">
                Use the populate script to import nursing ranks or manage directly in Firestore.
              </p>
            </div>

            <div class="pt-4 border-t">
              <h4 class="font-medium text-gray-900 mb-2">Current Configuration</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <div>Ranks: Managed in Firestore</div>
                <div>Project Types: {{ configStore.projectTypes.length }}</div>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Bulk Delete Dialog -->
    <Dialog
      :visible="showBulkDeleteDialog"
      @update:visible="showBulkDeleteDialog = $event"
      modal
      header="Bulk Delete Projects"
      :style="{ width: '90vw', maxWidth: '500px' }"
    >
      <div class="space-y-4">
        <div class="p-4 bg-red-50 border border-red-200 rounded-md">
          <div class="flex">
            <i class="pi pi-exclamation-triangle text-red-400 mr-2"></i>
            <div class="text-sm text-red-700">
              <strong>Warning:</strong> This action cannot be undone. All selected projects will be
              permanently deleted.
            </div>
          </div>
        </div>

        <p class="text-gray-600">
          This feature is coming soon. For now, projects can be deleted individually from the
          project list.
        </p>
      </div>

      <template #footer>
        <Button
          label="Close"
          icon="pi pi-times"
          class="p-button-text"
          @click="showBulkDeleteDialog = false"
        />
      </template>
    </Dialog>

    <!-- Department Configuration moved to Firestore -->

    <!-- Rank Configuration moved to Firestore -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useProjectsStore } from '@/stores/projects'
import { useConfigStore } from '@/stores/config'
import { useToast } from 'primevue/usetoast'

const projectsStore = useProjectsStore()
const configStore = useConfigStore()
const toast = useToast()

// Reactive data
const exportLoading = ref(false)
const showBulkDeleteDialog = ref(false)

onMounted(async () => {
  // Fetch latest data
  await projectsStore.fetchProjects()
})

async function exportProjects() {
  exportLoading.value = true

  try {
    // Create CSV content
    const headers = [
      'Project ID',
      'Title',
      'Department',
      'Unit',
      'Leader Name',
      'Leader Rank',
      'Leader Employee Number',
      'Status',
      'Type',
      'Commencement Date',
      'Completion Date',
      'Created Date',
    ]

    const csvContent = [
      headers.join(','),
      ...projectsStore.projects.map((project) =>
        [
          project.id,
          `"${project.projectTitle}"`,
          project.department,
          project.unit || '',
          `"${project.projectLeader.fullName}"`,
          project.projectLeader.rank,
          project.projectLeader.employeeNumber,
          project.progressStatus,
          project.projectType,
          project.commencementDate
            ? new Date(project.commencementDate.toDate()).toLocaleDateString()
            : '',
          project.completionDate
            ? new Date(project.completionDate.toDate()).toLocaleDateString()
            : '',
          project.createdAt ? new Date(project.createdAt.toDate()).toLocaleDateString() : '',
        ].join(','),
      ),
    ].join('\n')

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `ebp-projects-${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    window.URL.revokeObjectURL(url)

    toast.add({
      severity: 'success',
      summary: 'Export Successful',
      detail: 'Projects exported to CSV file',
      life: 3000,
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Export Failed',
      detail: 'Failed to export projects',
      life: 5000,
    })
  } finally {
    exportLoading.value = false
  }
}

// Department management moved to departments store
// Rank management moved to ranks store

async function saveConfiguration() {
  try {
    await configStore.saveConfig()

    toast.add({
      severity: 'success',
      summary: 'Configuration Saved',
      detail: 'Configuration changes have been saved',
      life: 3000,
    })

    // Rank config dialog removed
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Save Failed',
      detail: 'Failed to save configuration changes',
      life: 5000,
    })
  }
}
</script>
