<template>
  <div class="project-edit-page">
    <!-- Header -->
    <div class="page-header mb-4">
      <div class="flex justify-content-between align-items-center">
        <div>
          <h1 class="text-3xl font-bold text-color mb-2">Edit Project</h1>
          <p class="text-color-secondary">
            {{ project ? project.projectTitle : 'Loading...' }}
          </p>
        </div>
        <Button label="Back to Projects" icon="pi pi-arrow-left" text @click="goBack" />
      </div>
    </div>

    <!-- Edit Form -->
    <Card class="w-full">
      <template #content>
        <ProjectForm
          :initial-data="project"
          :is-edit="true"
          @submit="handleSubmit"
          @cancel="handleCancel"
        />
      </template>
    </Card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useToast } from 'primevue/usetoast'
import ProjectForm from '@/components/ProjectForm.vue'

const route = useRoute()
const router = useRouter()
const projectsStore = useProjectsStore()
const toast = useToast()

const project = ref(null)

onMounted(async () => {
  const projectData = await projectsStore.getProjectById(route.params.id)
  console.log('Project data in ProjectEdit:', projectData)
  project.value = projectData ? projectData : null;
})

async function handleSubmit(formData) {
  try {
    const projectId = project.value?.id || route.params.id
    await projectsStore.updateProject(projectId, formData)

    toast.add({
      severity: 'success',
      summary: 'Project Updated',
      detail: 'Project has been successfully updated',
      life: 3000,
    })

    // Navigate back to projects list
    router.push('/projects')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Update Failed',
      detail: error.message || 'Failed to update project',
      life: 5000,
    })
  }
}

function handleCancel() {
  goBack()
}

function goBack() {
  router.push('/projects')
}
</script>

<style scoped>
.project-edit-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  border-bottom: 1px solid var(--p-surface-200);
  padding-bottom: 1rem;
}

@media (max-width: 768px) {
  .project-edit-page {
    padding: 1rem;
  }

  .page-header .flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>
