import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { collection, getDocs, doc, setDoc, query, orderBy } from 'firebase/firestore'
import { db } from '@/firebase/config'

export const useRanksStore = defineStore('ranks', () => {
  // State
  const ranks = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Define the specific order for ranks
  const rankOrder = [
    "DOM / SNO / NC",
    "WM / ANC",
    "APN / NO",
    "RN",
    "EN"
  ]

  // Computed
  const rankOptions = computed(() => {
    // Sort ranks according to the predefined order
    const sortedRanks = [...ranks.value].sort((a, b) => {
      const indexA = rankOrder.indexOf(a)
      const indexB = rankOrder.indexOf(b)

      // If both ranks are in the predefined order, sort by their position
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB
      }

      // If only one rank is in the predefined order, prioritize it
      if (indexA !== -1) return -1
      if (indexB !== -1) return 1

      // If neither rank is in the predefined order, sort alphabetically
      return a.localeCompare(b)
    })

    return sortedRanks.map(rank => ({
      label: rank,
      value: rank
    }))
  })

  const ranksList = computed(() => {
    // Return ranks in the predefined order
    const sortedRanks = [...ranks.value].sort((a, b) => {
      const indexA = rankOrder.indexOf(a)
      const indexB = rankOrder.indexOf(b)

      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB
      }

      if (indexA !== -1) return -1
      if (indexB !== -1) return 1

      return a.localeCompare(b)
    })

    return sortedRanks
  })

  // Actions
  async function fetchRanks() {
    loading.value = true
    error.value = null
    try {
      const querySnapshot = await getDocs(collection(db, 'ranks'))

      // Combine all ranks from all documents
      const allRanks = []
      querySnapshot.docs.forEach(doc => {
        const data = doc.data()
        if (data.ranks && Array.isArray(data.ranks)) {
          allRanks.push(...data.ranks)
        }
      })

      // Remove duplicates and maintain the predefined order
      const uniqueRanks = [...new Set(allRanks)]
      ranks.value = uniqueRanks.sort((a, b) => {
        const indexA = rankOrder.indexOf(a)
        const indexB = rankOrder.indexOf(b)

        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB
        }

        if (indexA !== -1) return -1
        if (indexB !== -1) return 1

        return a.localeCompare(b)
      })

      console.log(`✅ Loaded ${ranks.value.length} ranks from Firestore`)
    } catch (err) {
      error.value = err.message
      console.error('Error fetching ranks:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function addRank(rank) {
    if (!ranks.value.includes(rank)) {
      ranks.value.push(rank)
      // Sort according to predefined order
      ranks.value = ranks.value.sort((a, b) => {
        const indexA = rankOrder.indexOf(a)
        const indexB = rankOrder.indexOf(b)

        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB
        }

        if (indexA !== -1) return -1
        if (indexB !== -1) return 1

        return a.localeCompare(b)
      })
      await saveRanks()
    }
  }

  async function removeRank(rank) {
    const index = ranks.value.indexOf(rank)
    if (index > -1) {
      ranks.value.splice(index, 1)
      await saveRanks()
    }
  }

  async function saveRanks() {
    loading.value = true
    error.value = null
    try {
      const docRef = doc(db, 'ranks', 'nursing-ranks')
      await setDoc(docRef, {
        ranks: ranks.value,
        updatedAt: new Date()
      }, { merge: true })

      console.log('✅ Ranks saved to Firestore')
    } catch (err) {
      error.value = err.message
      console.error('Error saving ranks:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updateRanks(newRanks) {
    const uniqueRanks = [...new Set(newRanks)]
    ranks.value = uniqueRanks.sort((a, b) => {
      const indexA = rankOrder.indexOf(a)
      const indexB = rankOrder.indexOf(b)

      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB
      }

      if (indexA !== -1) return -1
      if (indexB !== -1) return 1

      return a.localeCompare(b)
    })
    await saveRanks()
  }

  return {
    // State
    ranks,
    loading,
    error,

    // Computed
    rankOptions,
    ranksList,

    // Actions
    fetchRanks,
    addRank,
    removeRank,
    saveRanks,
    updateRanks
  }
})
