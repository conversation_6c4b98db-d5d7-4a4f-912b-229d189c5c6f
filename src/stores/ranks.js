import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { collection, getDocs, doc, setDoc, query, orderBy } from 'firebase/firestore'
import { db } from '@/firebase/config'

export const useRanksStore = defineStore('ranks', () => {
  // State
  const ranks = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Computed
  const rankOptions = computed(() => 
    ranks.value.map(rank => ({
      label: rank,
      value: rank
    })).sort((a, b) => a.label.localeCompare(b.label))
  )

  const ranksList = computed(() => 
    [...ranks.value].sort()
  )

  // Actions
  async function fetchRanks() {
    loading.value = true
    error.value = null
    try {
      const querySnapshot = await getDocs(collection(db, 'ranks'))
      
      // Combine all ranks from all documents
      const allRanks = []
      querySnapshot.docs.forEach(doc => {
        const data = doc.data()
        if (data.ranks && Array.isArray(data.ranks)) {
          allRanks.push(...data.ranks)
        }
      })
      
      // Remove duplicates and sort
      ranks.value = [...new Set(allRanks)].sort()
      
      console.log(`✅ Loaded ${ranks.value.length} ranks from Firestore`)
    } catch (err) {
      error.value = err.message
      console.error('Error fetching ranks:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function addRank(rank) {
    if (!ranks.value.includes(rank)) {
      ranks.value.push(rank)
      ranks.value.sort()
      await saveRanks()
    }
  }

  async function removeRank(rank) {
    const index = ranks.value.indexOf(rank)
    if (index > -1) {
      ranks.value.splice(index, 1)
      await saveRanks()
    }
  }

  async function saveRanks() {
    loading.value = true
    error.value = null
    try {
      const docRef = doc(db, 'ranks', 'nursing-ranks')
      await setDoc(docRef, {
        ranks: ranks.value,
        updatedAt: new Date()
      }, { merge: true })
      
      console.log('✅ Ranks saved to Firestore')
    } catch (err) {
      error.value = err.message
      console.error('Error saving ranks:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updateRanks(newRanks) {
    ranks.value = [...new Set(newRanks)].sort()
    await saveRanks()
  }

  return {
    // State
    ranks,
    loading,
    error,
    
    // Computed
    rankOptions,
    ranksList,
    
    // Actions
    fetchRanks,
    addRank,
    removeRank,
    saveRanks,
    updateRanks
  }
})
