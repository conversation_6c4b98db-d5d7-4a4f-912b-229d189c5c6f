import { ref } from 'vue'
import { defineStore } from 'pinia'
import { doc, getDoc, setDoc } from 'firebase/firestore'
import { db } from '@/firebase/config'

export const useConfigStore = defineStore('config', () => {
  // Note: Departments are now managed by the departments store
  // Note: Ranks are now managed by the ranks store

  const progressStatuses = ref([
    'Not Started',
    'In Progress',
    'Completed'
  ])

  const projectTypes = ref([
    'EBP',
    'Research',
    'CQI Project',
    'Other'
  ])

  const loading = ref(false)
  const error = ref(null)

  // Actions
  async function loadConfig() {
    loading.value = true
    error.value = null
    try {
      const configDoc = await getDoc(doc(db, 'config', 'dropdowns'))
      if (configDoc.exists()) {
        const data = configDoc.data()
        if (data.progressStatuses) progressStatuses.value = data.progressStatuses
        if (data.projectTypes) projectTypes.value = data.projectTypes
      }
    } catch (err) {
      error.value = err.message
      console.error('Error loading config:', err)
    } finally {
      loading.value = false
    }
  }

  async function saveConfig() {
    loading.value = true
    error.value = null
    try {
      await setDoc(doc(db, 'config', 'dropdowns'), {
        progressStatuses: progressStatuses.value,
        projectTypes: projectTypes.value,
        updatedAt: new Date()
      })
    } catch (err) {
      error.value = err.message
      console.error('Error saving config:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Department management moved to departments store
  // Rank management moved to ranks store

  return {
    progressStatuses,
    projectTypes,
    loading,
    error,
    loadConfig,
    saveConfig
  }
})
