import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { collection, getDocs, doc, setDoc, deleteDoc, query, orderBy } from 'firebase/firestore'
import { db } from '@/firebase/config'

export const useDepartmentsStore = defineStore('departments', () => {
  // State
  const departments = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Computed
  const departmentNames = computed(() => 
    departments.value.map(dept => dept.name).sort()
  )

  const departmentOptions = computed(() => 
    departments.value.map(dept => ({
      label: dept.name,
      value: dept.name
    })).sort((a, b) => a.label.localeCompare(b.label))
  )

  // Get units for a specific department
  const getUnitsForDepartment = computed(() => (departmentName) => {
    const dept = departments.value.find(d => d.name === departmentName)
    return dept ? dept.units.filter(unit => !unit.invisible) : []
  })

  const getUnitOptionsForDepartment = computed(() => (departmentName) => {
    const units = getUnitsForDepartment.value(departmentName)
    return units.map(unit => ({
      label: unit.name,
      value: unit.name
    })).sort((a, b) => a.label.localeCompare(b.label))
  })

  // Actions
  async function fetchDepartments() {
    loading.value = true
    error.value = null
    try {
      const q = query(collection(db, 'departments'), orderBy('name'))
      const querySnapshot = await getDocs(q)
      departments.value = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
      console.log(`✅ Loaded ${departments.value.length} departments from Firestore`)
    } catch (err) {
      error.value = err.message
      console.error('Error fetching departments:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function addDepartment(departmentData) {
    loading.value = true
    error.value = null
    try {
      const docRef = doc(collection(db, 'departments'), departmentData.name)
      await setDoc(docRef, {
        ...departmentData,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      // Add to local state
      departments.value.push({
        id: departmentData.name,
        ...departmentData,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      // Sort departments
      departments.value.sort((a, b) => a.name.localeCompare(b.name))
      
    } catch (err) {
      error.value = err.message
      console.error('Error adding department:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updateDepartment(departmentName, departmentData) {
    loading.value = true
    error.value = null
    try {
      const docRef = doc(db, 'departments', departmentName)
      await setDoc(docRef, {
        ...departmentData,
        updatedAt: new Date()
      }, { merge: true })
      
      // Update local state
      const index = departments.value.findIndex(d => d.name === departmentName)
      if (index !== -1) {
        departments.value[index] = {
          ...departments.value[index],
          ...departmentData,
          updatedAt: new Date()
        }
      }
      
    } catch (err) {
      error.value = err.message
      console.error('Error updating department:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteDepartment(departmentName) {
    loading.value = true
    error.value = null
    try {
      await deleteDoc(doc(db, 'departments', departmentName))
      
      // Remove from local state
      const index = departments.value.findIndex(d => d.name === departmentName)
      if (index !== -1) {
        departments.value.splice(index, 1)
      }
      
    } catch (err) {
      error.value = err.message
      console.error('Error deleting department:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function addUnitToDepartment(departmentName, unitData) {
    const dept = departments.value.find(d => d.name === departmentName)
    if (!dept) throw new Error('Department not found')
    
    const updatedUnits = [...dept.units, unitData]
    await updateDepartment(departmentName, {
      ...dept,
      units: updatedUnits
    })
  }

  async function removeUnitFromDepartment(departmentName, unitName) {
    const dept = departments.value.find(d => d.name === departmentName)
    if (!dept) throw new Error('Department not found')
    
    const updatedUnits = dept.units.filter(unit => unit.name !== unitName)
    await updateDepartment(departmentName, {
      ...dept,
      units: updatedUnits
    })
  }

  async function updateUnitInDepartment(departmentName, oldUnitName, newUnitData) {
    const dept = departments.value.find(d => d.name === departmentName)
    if (!dept) throw new Error('Department not found')
    
    const updatedUnits = dept.units.map(unit => 
      unit.name === oldUnitName ? { ...unit, ...newUnitData } : unit
    )
    
    await updateDepartment(departmentName, {
      ...dept,
      units: updatedUnits
    })
  }

  // Helper function to get department by name
  function getDepartmentByName(name) {
    return departments.value.find(d => d.name === name)
  }

  // Helper function to get unit by name within a department
  function getUnitByName(departmentName, unitName) {
    const dept = getDepartmentByName(departmentName)
    return dept ? dept.units.find(u => u.name === unitName) : null
  }

  return {
    // State
    departments,
    loading,
    error,
    
    // Computed
    departmentNames,
    departmentOptions,
    getUnitsForDepartment,
    getUnitOptionsForDepartment,
    
    // Actions
    fetchDepartments,
    addDepartment,
    updateDepartment,
    deleteDepartment,
    addUnitToDepartment,
    removeUnitFromDepartment,
    updateUnitInDepartment,
    
    // Helpers
    getDepartmentByName,
    getUnitByName
  }
})
