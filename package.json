{"name": "ebp-registry", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/", "setup:firebase": "node scripts/setup-firebase.js", "populate:departments": "node scripts/populate-departments.js"}, "dependencies": {"@primevue/themes": "^4.3.4", "date-fns": "^4.1.0", "firebase": "^11.8.1", "firestore-export-import": "^1.6.0", "marked": "^15.0.12", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.4", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "prettier": "3.5.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}