import { initializeApp } from 'firebase/app'
import { getFirestore, collection, doc, setDoc } from 'firebase/firestore'

// Firebase configuration (same as in your config.js)
const firebaseConfig = {
  apiKey: "AIzaSyD2BbJHZCiEr0FBzLhd6GpFqXRxmuP6SEs",
  authDomain: "ebp-project-registry.firebaseapp.com",
  projectId: "ebp-project-registry",
  storageBucket: "ebp-project-registry.firebasestorage.app",
  messagingSenderId: "83884410261",
  appId: "1:83884410261:web:2ee6ae76edf6329d23a29c"
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)
const db = getFirestore(app)

async function populateRanks() {
  try {
    // Define the ranks structure
    const ranksData = {
      ranks: [
        "DOM / SNO / NC",
        "WM / ANC", 
        "APN / NO",
        "RN",
        "EN"
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    console.log('Creating ranks collection with data:', ranksData)
    
    // Create ranks document in Firestore
    const docRef = doc(collection(db, 'ranks'), 'nursing-ranks')
    await setDoc(docRef, ranksData)
    
    console.log('✅ Successfully populated ranks collection in Firestore!')
    console.log(`📊 Created ranks document with ${ranksData.ranks.length} ranks`)
    console.log('📋 Ranks:', ranksData.ranks.join(', '))
    
  } catch (error) {
    console.error('❌ Error populating ranks:', error)
    process.exit(1)
  }
}

// Run the script
populateRanks()
  .then(() => {
    console.log('🎉 Script completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Script failed:', error)
    process.exit(1)
  })
