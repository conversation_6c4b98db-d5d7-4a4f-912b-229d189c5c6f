import { initializeApp } from 'firebase/app'
import { getFirestore, collection, doc, setDoc } from 'firebase/firestore'
import { readFileSync } from 'fs'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// Get current directory
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Firebase configuration (same as in your config.js)
const firebaseConfig = {
  apiKey: "AIzaSyD2BbJHZCiEr0FBzLhd6GpFqXRxmuP6SEs",
  authDomain: "ebp-project-registry.firebaseapp.com",
  projectId: "ebp-project-registry",
  storageBucket: "ebp-project-registry.firebasestorage.app",
  messagingSenderId: "83884410261",
  appId: "1:83884410261:web:2ee6ae76edf6329d23a29c"
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)
const db = getFirestore(app)

async function populateDepartments() {
  try {
    // Read units.json file
    const unitsPath = join(__dirname, '../units.json')
    const unitsData = JSON.parse(readFileSync(unitsPath, 'utf8'))

    // Extract departments and their units
    const departmentsMap = new Map()

    // Process each unit to group by department
    Object.entries(unitsData).forEach(([unitKey, unitData]) => {
      const deptName = unitData.dept
      const unitName = unitData.unit
      const email = unitData.email
      const access = unitData.access
      const invisible = unitData.invisible || false

      if (!departmentsMap.has(deptName)) {
        departmentsMap.set(deptName, {
          name: deptName,
          units: []
        })
      }

      departmentsMap.get(deptName).units.push({
        name: unitName,
        email: email,
        access: access,
        invisible: invisible
      })
    })

    // Convert to array and sort
    const departments = Array.from(departmentsMap.values()).sort((a, b) => a.name.localeCompare(b.name))

    console.log(`Found ${departments.length} departments with units:`)
    departments.forEach(dept => {
      console.log(`- ${dept.name}: ${dept.units.length} units`)
    })

    // Create departments collection in Firestore
    const batch = []
    for (const dept of departments) {
      const docRef = doc(collection(db, 'departments'), dept.name)
      batch.push(setDoc(docRef, {
        name: dept.name,
        units: dept.units,
        createdAt: new Date(),
        updatedAt: new Date()
      }))
    }

    // Execute all writes
    await Promise.all(batch)

    console.log('✅ Successfully populated departments collection in Firestore!')
    console.log(`📊 Created ${departments.length} department documents`)

    // Log summary
    const totalUnits = departments.reduce((sum, dept) => sum + dept.units.length, 0)
    console.log(`📋 Total units across all departments: ${totalUnits}`)

  } catch (error) {
    console.error('❌ Error populating departments:', error)
    process.exit(1)
  }
}

// Run the script
populateDepartments()
  .then(() => {
    console.log('🎉 Script completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Script failed:', error)
    process.exit(1)
  })
